External ReportSummary] Using JSESSIONID: A1779F6CEE2F3820FC4C9F7BC99C4E16
[2025-07-22T13:45:47.189Z] GET /local/recent-runs?limit=100 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-22T13:45:47.261Z] POST /api/external/login from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
POST /api/external/login
[External Login] Request body: { uid: '<EMAIL>', password: 'test' }
[External Login] Request headers: {
  host: 'localhost:3000',
  connection: 'keep-alive',
  'content-length': '58',
  pragma: 'no-cache',
  'cache-control': 'no-cache',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  dnt: '1',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  accept: '*/*',
  origin: 'http://localhost:3000',
  'sec-fetch-site': 'same-origin',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:3000/config/index.html',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en,ru;q=0.9,es;q=0.8,ru-RU;q=0.7,en-US;q=0.6,he;q=0.5,tr;q=0.4,it;q=0.3',
  cookie: 'accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-UhmE6OSPL59WTcxStFP1rURbttmFob8fEBmv4QtQnc; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.kNkuFjBgVTo12Z4-ARouTM-cGCRXoRBeoHN_HItcVKE; sessionId=c74cb947a33c10a09c4771aad6a390f74d141dddf98494240f4e85b4bba8cb07; AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; JSESSIONID=A1779F6CEE2F3820FC4C9F7BC99C4E16'
}
[External Login] Attempting login for user: <EMAIL>
[2025-07-22T13:45:47.272Z] POST /api/external/login from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
POST /api/external/login
[External Login] Request body: { uid: '<EMAIL>', password: 'test' }
[External Login] Request headers: {
  host: 'localhost:3000',
  connection: 'keep-alive',
  'content-length': '58',
  pragma: 'no-cache',
  'cache-control': 'no-cache',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  dnt: '1',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  accept: '*/*',
  origin: 'http://localhost:3000',
  'sec-fetch-site': 'same-origin',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:3000/config/index.html',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en,ru;q=0.9,es;q=0.8,ru-RU;q=0.7,en-US;q=0.6,he;q=0.5,tr;q=0.4,it;q=0.3',
  cookie: 'accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-UhmE6OSPL59WTcxStFP1rURbttmFob8fEBmv4QtQnc; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.kNkuFjBgVTo12Z4-ARouTM-cGCRXoRBeoHN_HItcVKE; sessionId=c74cb947a33c10a09c4771aad6a390f74d141dddf98494240f4e85b4bba8cb07; AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; JSESSIONID=A1779F6CEE2F3820FC4C9F7BC99C4E16'
}
[External Login] Attempting login for user: <EMAIL>
[External Login] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login
[External Login] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login
[External Login] External API response status: 200
[External Login] External API response headers: {
  'content-length': '704',
  'content-type': 'text/html;charset=UTF-8',
  date: 'Tue, 22 Jul 2025 13:45:48 GMT',
  'set-cookie': 'JSESSIONID=081F5F6FAD66237D02B922554BAB1F3A; Path=/AutoRun; HttpOnly'
}
[External Login] Original cookie: JSESSIONID=081F5F6FAD66237D02B922554BAB1F3A; Path=/AutoRun; HttpOnly
[External Login] Modified cookie: JSESSIONID=081F5F6FAD66237D02B922554BAB1F3A; Path=/; HttpOnly
[External Login] Forwarding cookies to client: 1 cookies with fixed Path
[External Login] Extracted JSESSIONID from Set-Cookie: 081F5F6FAD66237D02B922554BAB1F3A
[External Login] Stored JSESSIONID in session for future requests
[External Login] Set additional client-side cookie with path /
[External Login] Returning response with status: 200
[2025-07-22T13:45:48.357Z] GET /api/external/ReportSummary?tsn_id=17652 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportSummary
[External ReportSummary] Incoming request cookies: accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-UhmE6OSPL59WTcxStFP1rURbttmFob8fEBmv4QtQnc; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.kNkuFjBgVTo12Z4-ARouTM-cGCRXoRBeoHN_HItcVKE; sessionId=c74cb947a33c10a09c4771aad6a390f74d141dddf98494240f4e85b4bba8cb07; AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; JSESSIONID=081F5F6FAD66237D02B922554BAB1F3A
[External ReportSummary] Extracted JSESSIONID: 081F5F6FAD66237D02B922554BAB1F3A
[External ReportSummary] 🔍 Session Decision Process Starting...
[External ReportSummary] 🔍 User from JWT: <EMAIL>
[External ReportSummary] 🔍 Browser JSESSIONID: 081F5F6F...
[External ReportSummary] 🔍 Session JSESSIONID: 081F5F6F...
[External ReportSummary] 🔍 Cache Status: {
  "totalEntries": 0,
  "entries": []
}
[External ReportSummary] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: false
[CookieAuth] ❌ No cached session <NAME_EMAIL>
[External ReportSummary] ❌ No cached session available, falling back to browser cookie
[External ReportSummary] ✅ Using BROWSER_COOKIE JSESSIONID: 081F5F6F... (SOURCE: BROWSER_COOKIE)
[External ReportSummary] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=17652
[External ReportSummary] Using JSESSIONID: 081F5F6FAD66237D02B922554BAB1F3A
[External Login] External API response status: 200
[External Login] External API response headers: {
  'content-length': '704',
  'content-type': 'text/html;charset=UTF-8',
  date: 'Tue, 22 Jul 2025 13:45:48 GMT',
  'set-cookie': 'JSESSIONID=5852EF3786CA4BD8FDC12987C40EA7B1; Path=/AutoRun; HttpOnly'
}
[External Login] Original cookie: JSESSIONID=5852EF3786CA4BD8FDC12987C40EA7B1; Path=/AutoRun; HttpOnly
[External Login] Modified cookie: JSESSIONID=5852EF3786CA4BD8FDC12987C40EA7B1; Path=/; HttpOnly
[External Login] Forwarding cookies to client: 1 cookies with fixed Path
[External Login] Extracted JSESSIONID from Set-Cookie: 5852EF3786CA4BD8FDC12987C40EA7B1
[External Login] Stored JSESSIONID in session for future requests
[External Login] Set additional client-side cookie with path /
[External Login] Returning response with status: 200
[2025-07-22T13:45:48.387Z] GET /api/external/ReportSummary?tsn_id=17652 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportSummary
[External ReportSummary] Incoming request cookies: accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-UhmE6OSPL59WTcxStFP1rURbttmFob8fEBmv4QtQnc; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.kNkuFjBgVTo12Z4-ARouTM-cGCRXoRBeoHN_HItcVKE; sessionId=c74cb947a33c10a09c4771aad6a390f74d141dddf98494240f4e85b4bba8cb07; AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; JSESSIONID=5852EF3786CA4BD8FDC12987C40EA7B1
[External ReportSummary] Extracted JSESSIONID: 5852EF3786CA4BD8FDC12987C40EA7B1
[External ReportSummary] 🔍 Session Decision Process Starting...
[External ReportSummary] 🔍 User from JWT: <EMAIL>
[External ReportSummary] 🔍 Browser JSESSIONID: 5852EF37...
[External ReportSummary] 🔍 Session JSESSIONID: 5852EF37...
[External ReportSummary] 🔍 Cache Status: {
  "totalEntries": 0,
  "entries": []
}
[External ReportSummary] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: false
[CookieAuth] ❌ No cached session <NAME_EMAIL>
[External ReportSummary] ❌ No cached session available, falling back to browser cookie
[External ReportSummary] ✅ Using BROWSER_COOKIE JSESSIONID: 5852EF37... (SOURCE: BROWSER_COOKIE)
[External ReportSummary] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=17652
[External ReportSummary] Using JSESSIONID: 5852EF3786CA4BD8FDC12987C40EA7B1
[2025-07-22T13:45:48.662Z] GET /local/test-details/17652? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/test-details/:tsn_id
Fetching test details for tsn_id: 17652
[DEBUG] Database connection status: {
  connected: false,
  threadId: null,
  database: undefined,
  connectionId: null
}
[DEBUG] Fetching test session details for tsn_id: 17652
[2025-07-22T13:45:49.186Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 100 sessions in 20 batches...
[DEBUG] getTestSessionDetails: About to query test_result for tsn_id: 17652
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 50 sessions in 10 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
[2025-07-22T13:45:57.505Z] POST /auth/logout from ::1
✅ Session revoked: c74cb947a33c10a09c4771aad6a390f74d141dddf98494240f4e85b4bba8cb07
📝 SESSION_LOGOUT: <EMAIL> (c74cb947a33c10a09c4771aad6a390f74d141dddf98494240f4e85b4bba8cb07)
✅ User logged out: c74cb947a33c10a09c4771aad6a390f74d141dddf98494240f4e85b4bba8cb07
✅ Logout successful: c74cb947a33c10a09c4771aad6a390f74d141dddf98494240f4e85b4bba8cb07
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[DEBUG] getTestSessionDetails: test_result query for tsn_id: 17652 returned 3 rows.
[DEBUG] getTestSessionDetails: First row from test_result: {"tc_id":"3180","seq_index":"1","outcome":"P","creation_time":"2025-07-22 12:04:34.798","cnt":"24977495","test_case_name":"Check pending feeds","description":"check NO pending feeds\r\\n\r\\nbk copy:  3228","input_data":"ssh jps-qa10-app01   echo 
qa02 \" \" 17652 \" \" new started >> /home/<USER>/TA/tempfilelog.txt;  cat /home/<USER>/TA/qa02_nj_properties","output_data":"<properties>\\n\\n<env>http://nje-qa02-app01.lab.wagerworks.com</env>\\n\\n<jdbc_driver>com.mysql.jdbc.Driver</jdbc_driver>\\n<jdbc_username>njeqa02</jdbc_username>\\n<jdbc_password>njeqa02</jdbc_password>\\n<jdbc_url>*********************************************************************</jdbc_url>\\n<jdbc_host>rno-ms-db01.lab.wagerworks.com</jdbc_host>\\n</properties>\\n","error_message":"NULL"}
Retrieved test details for tsn_id: 17652
Test case count: 3
First test case: {
  "tc_id": "3180",
  "test_case_name": "Check pending feeds",
  "description": "check NO pending feeds\r\\n\r\\nbk copy:  3228",
  "outcome": "P",
  "status": "Passed",
  "creation_time": "2025-07-22 12:04:34.798",
  "cnt": "24977495",
  "seq_index": "1",
  "input_data": "ssh jps-qa10-app01   echo qa02 \" \" 17652 \" \" new started >> /home/<USER>/TA/tempfilelog.txt;  cat /home/<USER>/TA/qa02_nj_properties",
  "output_data": "<properties>\\n\\n<env>http://nje-qa02-app01.lab.wagerworks.com</env>\\n\\n<jdbc_driver>com.mysql.jdbc.Driver</jdbc_driver>\\n<jdbc_username>njeqa02</jdbc_username>\\n<jdbc_password>njeqa02</jdbc_password>\\n<jdbc_url>*********************************************************************</jdbc_url>\\n<jdbc_host>rno-ms-db01.lab.wagerworks.com</jdbc_host>\\n</properties>\\n",
  "error_message": "NULL",
  "failure_context": null,
  "source": "database"
}
[2025-07-22T13:46:01.195Z] GET /local/recent-runs?limit=100 from ::1
[2025-07-22T13:46:03.191Z] GET /local/recent-runs?limit=100 from ::1
[2025-07-22T13:46:04.356Z] POST /auth/login from ::1
Loaded 7 allowed users from C:\Dev\smarttest\frontend\server\config\allowed-users.json
✅ Session created: 6398e74234bf17f917165819525f0ff6d781afe0bb4cb321975bb5a190e72baa for user: <EMAIL>
✅ AUTH_SUCCESS: <EMAIL> from ::1
✅ Login successful: <EMAIL> from ::1
[2025-07-22T13:46:05.199Z] GET /local/recent-runs?limit=100 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-22T13:46:05.205Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 50 sessions in 10 batches...
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 100 sessions in 20 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
[2025-07-22T13:46:14.123Z] GET /api/external/ReportSummary?tsn_id=17653 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportSummary
[External ReportSummary] Incoming request cookies: AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; JSESSIONID=5852EF3786CA4BD8FDC12987C40EA7B1; accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yevRXYLD7hIGsvuCU5JMkF9kg3OnSSR0qSLBDQz0rrk; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.iY9gkrn--ezPYIrbSoZhk40ia3KgbPYJ6vlaHs7Duvs; sessionId=6398e74234bf17f917165819525f0ff6d781afe0bb4cb321975bb5a190e72baa
[External ReportSummary] Extracted JSESSIONID: 5852EF3786CA4BD8FDC12987C40EA7B1
[External ReportSummary] 🔍 Session Decision Process Starting...
[External ReportSummary] 🔍 User from JWT: <EMAIL>
[External ReportSummary] 🔍 Browser JSESSIONID: 5852EF37...
[External ReportSummary] 🔍 Session JSESSIONID: NONE...
[External ReportSummary] 🔍 Cache Status: {
  "totalEntries": 0,
  "entries": []
}
[External ReportSummary] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: false
[CookieAuth] ❌ No cached session <NAME_EMAIL>
[External ReportSummary] ❌ No cached session available, falling back to browser cookie
[External ReportSummary] ✅ Using BROWSER_COOKIE JSESSIONID: 5852EF37... (SOURCE: BROWSER_COOKIE)
[External ReportSummary] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=17653
[External ReportSummary] Using JSESSIONID: 5852EF3786CA4BD8FDC12987C40EA7B1
[2025-07-22T13:46:14.138Z] GET /api/external/ReportSummary?tsn_id=17653 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportSummary
[External ReportSummary] Incoming request cookies: AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; JSESSIONID=5852EF3786CA4BD8FDC12987C40EA7B1; accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yevRXYLD7hIGsvuCU5JMkF9kg3OnSSR0qSLBDQz0rrk; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.iY9gkrn--ezPYIrbSoZhk40ia3KgbPYJ6vlaHs7Duvs; sessionId=6398e74234bf17f917165819525f0ff6d781afe0bb4cb321975bb5a190e72baa
[External ReportSummary] Extracted JSESSIONID: 5852EF3786CA4BD8FDC12987C40EA7B1
[External ReportSummary] 🔍 Session Decision Process Starting...
[External ReportSummary] 🔍 User from JWT: <EMAIL>
[External ReportSummary] 🔍 Browser JSESSIONID: 5852EF37...
[External ReportSummary] 🔍 Session JSESSIONID: 5852EF37...
[External ReportSummary] 🔍 Cache Status: {
  "totalEntries": 0,
  "entries": []
}
[External ReportSummary] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: false
[CookieAuth] ❌ No cached session <NAME_EMAIL>
[External ReportSummary] ❌ No cached session available, falling back to browser cookie
[External ReportSummary] ✅ Using BROWSER_COOKIE JSESSIONID: 5852EF37... (SOURCE: BROWSER_COOKIE)
[External ReportSummary] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=17653
[External ReportSummary] Using JSESSIONID: 5852EF3786CA4BD8FDC12987C40EA7B1
[2025-07-22T13:46:14.641Z] POST /api/external/login from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
POST /api/external/login
[External Login] Request body: { uid: '<EMAIL>', password: 'test' }
[External Login] Request headers: {
  host: 'localhost:3000',
  connection: 'keep-alive',
  'content-length': '58',
  pragma: 'no-cache',
  'cache-control': 'no-cache',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  dnt: '1',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  accept: '*/*',
  origin: 'http://localhost:3000',
  'sec-fetch-site': 'same-origin',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:3000/config/index.html',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en,ru;q=0.9,es;q=0.8,ru-RU;q=0.7,en-US;q=0.6,he;q=0.5,tr;q=0.4,it;q=0.3',
  cookie: 'AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; JSESSIONID=5852EF3786CA4BD8FDC12987C40EA7B1; accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yevRXYLD7hIGsvuCU5JMkF9kg3OnSSR0qSLBDQz0rrk; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.iY9gkrn--ezPYIrbSoZhk40ia3KgbPYJ6vlaHs7Duvs; sessionId=6398e74234bf17f917165819525f0ff6d781afe0bb4cb321975bb5a190e72baa'
}
[External Login] Attempting login for user: <EMAIL>
[2025-07-22T13:46:14.659Z] POST /api/external/login from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
POST /api/external/login
[External Login] Request body: { uid: '<EMAIL>', password: 'test' }
[External Login] Request headers: {
  host: 'localhost:3000',
  connection: 'keep-alive',
  'content-length': '58',
  pragma: 'no-cache',
  'cache-control': 'no-cache',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  dnt: '1',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  accept: '*/*',
  origin: 'http://localhost:3000',
  'sec-fetch-site': 'same-origin',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:3000/config/index.html',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en,ru;q=0.9,es;q=0.8,ru-RU;q=0.7,en-US;q=0.6,he;q=0.5,tr;q=0.4,it;q=0.3',
  cookie: 'AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; JSESSIONID=5852EF3786CA4BD8FDC12987C40EA7B1; accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yevRXYLD7hIGsvuCU5JMkF9kg3OnSSR0qSLBDQz0rrk; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.iY9gkrn--ezPYIrbSoZhk40ia3KgbPYJ6vlaHs7Duvs; sessionId=6398e74234bf17f917165819525f0ff6d781afe0bb4cb321975bb5a190e72baa'
}
[External Login] Attempting login for user: <EMAIL>
[External Login] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login
[External Login] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login
[External Login] External API response status: 200
[External Login] External API response headers: {
  'content-length': '704',
  'content-type': 'text/html;charset=UTF-8',
  date: 'Tue, 22 Jul 2025 13:46:16 GMT',
  'set-cookie': 'JSESSIONID=2A606B519D4E93345491799B0DD8CFF0; Path=/AutoRun; HttpOnly'
}
[External Login] Original cookie: JSESSIONID=2A606B519D4E93345491799B0DD8CFF0; Path=/AutoRun; HttpOnly
[External Login] Modified cookie: JSESSIONID=2A606B519D4E93345491799B0DD8CFF0; Path=/; HttpOnly
[External Login] Forwarding cookies to client: 1 cookies with fixed Path
[External Login] Extracted JSESSIONID from Set-Cookie: 2A606B519D4E93345491799B0DD8CFF0
[External Login] Stored JSESSIONID in session for future requests
[External Login] Set additional client-side cookie with path /
[External Login] Returning response with status: 200
[External Login] External API response status: 200
[External Login] External API response headers: {
  'content-length': '704',
  'content-type': 'text/html;charset=UTF-8',
  date: 'Tue, 22 Jul 2025 13:46:16 GMT',
  'set-cookie': 'JSESSIONID=DC75E18CC93309E3EF1C4230D2AE5B85; Path=/AutoRun; HttpOnly'
}
[External Login] Original cookie: JSESSIONID=DC75E18CC93309E3EF1C4230D2AE5B85; Path=/AutoRun; HttpOnly
[External Login] Modified cookie: JSESSIONID=DC75E18CC93309E3EF1C4230D2AE5B85; Path=/; HttpOnly
[External Login] Forwarding cookies to client: 1 cookies with fixed Path
[External Login] Extracted JSESSIONID from Set-Cookie: DC75E18CC93309E3EF1C4230D2AE5B85
[External Login] Stored JSESSIONID in session for future requests
[External Login] Set additional client-side cookie with path /
[External Login] Returning response with status: 200
[2025-07-22T13:46:15.781Z] GET /api/external/ReportSummary?tsn_id=17653 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportSummary
[External ReportSummary] Incoming request cookies: AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yevRXYLD7hIGsvuCU5JMkF9kg3OnSSR0qSLBDQz0rrk; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.iY9gkrn--ezPYIrbSoZhk40ia3KgbPYJ6vlaHs7Duvs; sessionId=6398e74234bf17f917165819525f0ff6d781afe0bb4cb321975bb5a190e72baa; JSESSIONID=DC75E18CC93309E3EF1C4230D2AE5B85
[External ReportSummary] Extracted JSESSIONID: DC75E18CC93309E3EF1C4230D2AE5B85
[External ReportSummary] 🔍 Session Decision Process Starting...
[External ReportSummary] 🔍 User from JWT: <EMAIL>
[External ReportSummary] 🔍 Browser JSESSIONID: DC75E18C...
[External ReportSummary] 🔍 Session JSESSIONID: DC75E18C...
[External ReportSummary] 🔍 Cache Status: {
  "totalEntries": 0,
  "entries": []
}
[External ReportSummary] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: false
[CookieAuth] ❌ No cached session <NAME_EMAIL>
[External ReportSummary] ❌ No cached session available, falling back to browser cookie
[External ReportSummary] ✅ Using BROWSER_COOKIE JSESSIONID: DC75E18C... (SOURCE: BROWSER_COOKIE)
[External ReportSummary] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=17653
[External ReportSummary] Using JSESSIONID: DC75E18CC93309E3EF1C4230D2AE5B85
[2025-07-22T13:46:15.792Z] GET /api/external/ReportSummary?tsn_id=17653 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: write
GET /api/external/ReportSummary
[External ReportSummary] Incoming request cookies: AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yevRXYLD7hIGsvuCU5JMkF9kg3OnSSR0qSLBDQz0rrk; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.iY9gkrn--ezPYIrbSoZhk40ia3KgbPYJ6vlaHs7Duvs; sessionId=6398e74234bf17f917165819525f0ff6d781afe0bb4cb321975bb5a190e72baa; JSESSIONID=DC75E18CC93309E3EF1C4230D2AE5B85
[External ReportSummary] Extracted JSESSIONID: DC75E18CC93309E3EF1C4230D2AE5B85
[External ReportSummary] 🔍 Session Decision Process Starting...
[External ReportSummary] 🔍 User from JWT: <EMAIL>
[External ReportSummary] 🔍 Browser JSESSIONID: DC75E18C...
[External ReportSummary] 🔍 Session JSESSIONID: DC75E18C...
[External ReportSummary] 🔍 Cache Status: {
  "totalEntries": 0,
  "entries": []
}
[External ReportSummary] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Checking cached session for user: <EMAIL>
[CookieAuth] 🔍 Cache entry exists: false
[CookieAuth] ❌ No cached session <NAME_EMAIL>
[External ReportSummary] ❌ No cached session available, falling back to browser cookie
[External ReportSummary] ✅ Using BROWSER_COOKIE JSESSIONID: DC75E18C... (SOURCE: BROWSER_COOKIE)
[External ReportSummary] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=17653
[External ReportSummary] Using JSESSIONID: DC75E18CC93309E3EF1C4230D2AE5B85
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-22T13:46:16.089Z] GET /local/test-details/17653? from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/test-details/:tsn_id
Fetching test details for tsn_id: 17653
[DEBUG] Database connection status: {
  connected: false,
  threadId: null,
  database: undefined,
  connectionId: null
}
[DEBUG] Fetching test session details for tsn_id: 17653
[2025-07-22T13:46:19.194Z] GET /local/recent-runs?limit=100 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-22T13:46:21.189Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
[DEBUG] getTestSessionDetails: About to query test_result for tsn_id: 17653
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 100 sessions in 20 batches...
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 50 sessions in 10 batches...
[DEBUG] getTestSessionDetails: test_result query for tsn_id: 17653 returned 3 rows.
[DEBUG] getTestSessionDetails: First row from test_result: {"tc_id":"3180","seq_index":"1","outcome":"P","creation_time":"2025-07-22 13:25:27.808","cnt":"24979848","test_case_name":"Check pending feeds","description":"check NO pending feeds\r\\n\r\\nbk copy:  3228","input_data":"ssh jps-qa10-app01   echo 
qa02 \" \" 17653 \" \" new started >> /home/<USER>/TA/tempfilelog.txt;  cat /home/<USER>/TA/qa02_nj_properties","output_data":"<properties>\\n\\n<env>http://nje-qa02-app01.lab.wagerworks.com</env>\\n\\n<jdbc_driver>com.mysql.jdbc.Driver</jdbc_driver>\\n<jdbc_username>njeqa02</jdbc_username>\\n<jdbc_password>njeqa02</jdbc_password>\\n<jdbc_url>*********************************************************************</jdbc_url>\\n<jdbc_host>rno-ms-db01.lab.wagerworks.com</jdbc_host>\\n</properties>\\n","error_message":"NULL"}
Retrieved test details for tsn_id: 17653
Test case count: 3
First test case: {
  "tc_id": "3180",
  "test_case_name": "Check pending feeds",
  "description": "check NO pending feeds\r\\n\r\\nbk copy:  3228",
  "outcome": "P",
  "status": "Passed",
  "creation_time": "2025-07-22 13:25:27.808",
  "cnt": "24979848",
  "seq_index": "1",
  "input_data": "ssh jps-qa10-app01   echo qa02 \" \" 17653 \" \" new started >> /home/<USER>/TA/tempfilelog.txt;  cat /home/<USER>/TA/qa02_nj_properties",
  "output_data": "<properties>\\n\\n<env>http://nje-qa02-app01.lab.wagerworks.com</env>\\n\\n<jdbc_driver>com.mysql.jdbc.Driver</jdbc_driver>\\n<jdbc_username>njeqa02</jdbc_username>\\n<jdbc_password>njeqa02</jdbc_password>\\n<jdbc_url>*********************************************************************</jdbc_url>\\n<jdbc_host>rno-ms-db01.lab.wagerworks.com</jdbc_host>\\n</properties>\\n",
  "error_message": "NULL",
  "failure_context": null,
  "source": "database"
}
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-22T13:46:33.207Z] GET /local/recent-runs?limit=100 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-22T13:46:37.189Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 100 sessions in 20 batches...
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 50 sessions in 10 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
[2025-07-22T13:46:47.224Z] GET /local/recent-runs?limit=100 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-22T13:46:53.211Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 50 sessions in 10 batches...
[2025-07-22T13:47:01.215Z] GET /local/recent-runs?limit=100 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 100 sessions in 20 batches...
[2025-07-22T13:47:09.321Z] GET /local/recent-runs?limit=50&type=single_case from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL: 
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-22T13:47:15.214Z] GET /local/recent-runs?limit=100 from ::1
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
✅ JWT Token validated for user: <EMAIL>
✅ Permission granted: <EMAIL> has permissions: read
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100 }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 50 sessions in 10 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-22 13:25:22, end_ts: 2025-07-22 13:25:32
Row 2 start_ts: 2025-07-22 12:04:29, end_ts: 2025-07-22 12:04:39
Row 3 start_ts: 2025-07-22 01:41:50, end_ts: 2025-07-22 01:41:56
⏳ Processing 100 sessions in 20 batches...
