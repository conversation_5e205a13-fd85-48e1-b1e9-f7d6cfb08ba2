{"name": "smarttest-auth-tests", "version": "1.0.0", "description": "Authentication system tests for SmartTest", "scripts": {"test": "jest", "test:auth": "jest auth-rejection.test.js", "test:security": "jest security-integration.test.js", "test:all": "jest --runInBand", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "TEST_VERBOSE=1 jest --verbose", "test:ci": "jest --ci --coverage --watchAll=false"}, "devDependencies": {"jest": "^29.0.0", "supertest": "^6.3.0", "@types/jest": "^29.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["../auth/**/*.js", "../middleware/**/*.js", "../routes/**/*.js", "!../node_modules/**"], "coverageDirectory": "./coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/*.test.js"], "setupFilesAfterEnv": ["./test-setup.js"]}}