<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartTest Admin - User Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #0078d7, #005a9e);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin-bottom: 10px;
        }

        .auth-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .main-content {
            display: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #0078d7;
            margin-bottom: 5px;
        }

        .section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #0078d7;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e1e1e1;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            background: #0078d7;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #005a9e;
        }

        .btn-danger {
            background: #d13438;
        }

        .btn-danger:hover {
            background: #a02d30;
        }

        .btn-success {
            background: #107c10;
        }

        .btn-success:hover {
            background: #0e6e0e;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .users-table th,
        .users-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .users-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .users-table tr:hover {
            background: #f8f9fa;
        }

        .status-active {
            color: #107c10;
            font-weight: bold;
        }

        .status-inactive {
            color: #d13438;
            font-weight: bold;
        }

        .role-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .role-admin {
            background: #fff4ce;
            color: #8a6914;
        }

        .role-tester {
            background: #cff4fc;
            color: #055160;
        }

        .role-viewer {
            background: #d1ecf1;
            color: #0c5460;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .users-table {
                font-size: 12px;
            }
            
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SmartTest Admin Panel</h1>
            <p>User Management & System Administration</p>
            <div style="position: absolute; top: 20px; right: 20px;">
                <button class="btn" onclick="adminLogout()" style="background: #d13438;">Logout</button>
            </div>
        </div>

        <!-- Authentication Section -->
        <div class="auth-section" id="authSection">
            <h2>Admin Authentication Required</h2>
            <div class="form-group">
                <label for="adminEmail">Admin Email:</label>
                <input type="email" id="adminEmail" placeholder="Enter your admin email">
            </div>
            <div class="form-group">
                <label for="adminPassword">Password:</label>
                <input type="password" id="adminPassword" placeholder="Enter your password">
            </div>
            <button class="btn" onclick="adminLogin()">Login</button>
            <div id="authError" class="alert alert-error" style="display: none;"></div>
        </div>

        <!-- Main Content (hidden until authenticated) -->
        <div class="main-content" id="mainContent">
            <!-- System Statistics -->
            <div class="stats-grid" id="statsGrid">
                <!-- Stats will be loaded here -->
            </div>

            <!-- Add User Section -->
            <div class="section">
                <h2>Add New User</h2>
                <div class="form-group">
                    <label for="newUserEmail">Email:</label>
                    <input type="email" id="newUserEmail" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="newUserPassword">Password:</label>
                    <input type="password" id="newUserPassword" placeholder="Enter password">
                </div>
                <div class="form-group">
                    <label for="newUserRole">Role:</label>
                    <select id="newUserRole">
                        <option value="viewer">Viewer</option>
                        <option value="tester">Tester</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="newUserName">Display Name (optional):</label>
                    <input type="text" id="newUserName" placeholder="Full name">
                </div>
                <button class="btn btn-success" onclick="addUser()">Add User</button>
            </div>

            <!-- Users List -->
            <div class="section">
                <h2>Manage Users</h2>
                <button class="btn" onclick="loadUsers()">Refresh Users</button>
                <div id="usersContainer">
                    <div class="loading">Loading users...</div>
                </div>
            </div>

            <!-- Alerts -->
            <div id="alertContainer"></div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditModal()">&times;</span>
            <h2>Edit User</h2>
            <div class="form-group">
                <label for="editUserEmail">Email:</label>
                <input type="email" id="editUserEmail" readonly>
            </div>
            <div class="form-group">
                <label for="editUserPassword">New Password (leave blank to keep current):</label>
                <input type="password" id="editUserPassword" placeholder="Enter new password">
            </div>
            <div class="form-group">
                <label for="editUserRole">Role:</label>
                <select id="editUserRole">
                    <option value="viewer">Viewer</option>
                    <option value="tester">Tester</option>
                    <option value="admin">Admin</option>
                </select>
            </div>
            <div class="form-group">
                <label for="editUserName">Display Name:</label>
                <input type="text" id="editUserName" placeholder="Full name">
            </div>
            <button class="btn btn-success" onclick="updateUser()">Update User</button>
            <button class="btn" onclick="closeEditModal()">Cancel</button>
        </div>
    </div>

    <!-- Load unified auth client -->
    <script src="../shared/auth/unified-auth-client.js"></script>

    <script>
        // Admin interface JavaScript will be embedded here
        let adminCredentials = null;
        let currentEditUser = null;
        let unifiedAuthClient = null;

        // Initialize unified auth client
        function initUnifiedAuth() {
            if (window.unifiedAuthClient && !unifiedAuthClient) {
                unifiedAuthClient = window.unifiedAuthClient;
                console.log('✅ Admin: Connected to unified auth client');

                // Check if already authenticated
                if (unifiedAuthClient.isAuthenticated) {
                    const user = unifiedAuthClient.getCurrentUser();
                    if (user && user.role === 'admin') {
                        document.getElementById('authSection').style.display = 'none';
                        document.getElementById('mainContent').style.display = 'block';
                        loadDashboard();
                    }
                }
                return true;
            }
            return false;
        }

        // Admin login
        async function adminLogin() {
            const email = document.getElementById('adminEmail').value;
            const password = document.getElementById('adminPassword').value;

            if (!email || !password) {
                showAuthError('Please enter both email and password');
                return;
            }

            try {
                // Initialize unified auth client if not already done
                if (!unifiedAuthClient) {
                    initUnifiedAuth();
                }

                if (unifiedAuthClient) {
                    // Use unified auth client for login
                    const result = await unifiedAuthClient.login(email, password);

                    if (result.success && result.user.role === 'admin') {
                        document.getElementById('authSection').style.display = 'none';
                        document.getElementById('mainContent').style.display = 'block';
                        loadDashboard();
                    } else if (result.success && result.user.role !== 'admin') {
                        showAuthError('Admin access required. Your role: ' + result.user.role);
                    } else {
                        showAuthError(result.error || 'Authentication failed');
                    }
                } else {
                    // Fallback to legacy authentication
                    adminCredentials = { uid: email, password: password };

                    const response = await fetch('/admin/status', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include'
                    });

                    if (response.ok) {
                        document.getElementById('authSection').style.display = 'none';
                        document.getElementById('mainContent').style.display = 'block';
                        loadDashboard();
                    } else {
                        const error = await response.json();
                        showAuthError(error.message || 'Authentication failed');
                    }
                }
            } catch (error) {
                showAuthError('Connection error: ' + error.message);
            }
        }

        // Show authentication error
        function showAuthError(message) {
            const errorDiv = document.getElementById('authError');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // Load dashboard data
        async function loadDashboard() {
            await loadStats();
            await loadUsers();
        }

        // Load system statistics
        async function loadStats() {
            try {
                const response = await makeAuthenticatedRequest('/admin/status');
                if (response.ok) {
                    const data = await response.json();
                    displayStats(data.data);
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Display statistics
        function displayStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.totalUsers}</div>
                    <div>Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.activeUsers}</div>
                    <div>Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.inactiveUsers}</div>
                    <div>Inactive Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Math.floor(stats.sessionTimeout / 60)}</div>
                    <div>Session Timeout (min)</div>
                </div>
            `;
        }

        // Load users list
        async function loadUsers() {
            try {
                const response = await makeAuthenticatedRequest('/admin/users');
                if (response.ok) {
                    const data = await response.json();
                    displayUsers(data.data);
                } else {
                    showAlert('Error loading users', 'error');
                }
            } catch (error) {
                showAlert('Connection error: ' + error.message, 'error');
            }
        }

        // Display users table
        function displayUsers(users) {
            const container = document.getElementById('usersContainer');

            if (users.length === 0) {
                container.innerHTML = '<p>No users found.</p>';
                return;
            }

            let html = `
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>Email</th>
                            <th>Name</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            users.forEach(user => {
                const statusClass = user.active ? 'status-active' : 'status-inactive';
                const statusText = user.active ? 'Active' : 'Inactive';
                const roleClass = `role-${user.role}`;
                const createdDate = new Date(user.created).toLocaleDateString();

                html += `
                    <tr>
                        <td>${user.uid}</td>
                        <td>${user.name || '-'}</td>
                        <td><span class="role-badge ${roleClass}">${user.role}</span></td>
                        <td><span class="${statusClass}">${statusText}</span></td>
                        <td>${createdDate}</td>
                        <td>
                            <button class="btn" onclick="editUser('${user.uid}')">Edit</button>
                            ${user.active ?
                                `<button class="btn btn-danger" onclick="deactivateUser('${user.uid}')">Deactivate</button>` :
                                `<button class="btn btn-success" onclick="activateUser('${user.uid}')">Activate</button>`
                            }
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // Add new user
        async function addUser() {
            const email = document.getElementById('newUserEmail').value;
            const password = document.getElementById('newUserPassword').value;
            const role = document.getElementById('newUserRole').value;
            const name = document.getElementById('newUserName').value;

            if (!email || !password) {
                showAlert('Email and password are required', 'error');
                return;
            }

            try {
                const response = await makeAuthenticatedRequest('/admin/users', 'POST', {
                    uid: email,
                    password: password,
                    role: role,
                    name: name
                });

                if (response.ok) {
                    showAlert('User added successfully', 'success');
                    clearAddUserForm();
                    loadUsers();
                    loadStats();
                } else {
                    const error = await response.json();
                    showAlert(error.message || 'Failed to add user', 'error');
                }
            } catch (error) {
                showAlert('Connection error: ' + error.message, 'error');
            }
        }

        // Clear add user form
        function clearAddUserForm() {
            document.getElementById('newUserEmail').value = '';
            document.getElementById('newUserPassword').value = '';
            document.getElementById('newUserRole').value = 'viewer';
            document.getElementById('newUserName').value = '';
        }

        // Edit user
        async function editUser(uid) {
            try {
                const response = await makeAuthenticatedRequest(`/admin/users/${encodeURIComponent(uid)}`);
                if (response.ok) {
                    const data = await response.json();
                    const user = data.data;

                    currentEditUser = uid;
                    document.getElementById('editUserEmail').value = user.uid;
                    document.getElementById('editUserPassword').value = '';
                    document.getElementById('editUserRole').value = user.role;
                    document.getElementById('editUserName').value = user.name || '';

                    document.getElementById('editModal').style.display = 'block';
                } else {
                    showAlert('Failed to load user details', 'error');
                }
            } catch (error) {
                showAlert('Connection error: ' + error.message, 'error');
            }
        }

        // Update user
        async function updateUser() {
            if (!currentEditUser) return;

            const password = document.getElementById('editUserPassword').value;
            const role = document.getElementById('editUserRole').value;
            const name = document.getElementById('editUserName').value;

            const updates = { role, name };
            if (password) {
                updates.password = password;
            }

            try {
                const response = await makeAuthenticatedRequest(
                    `/admin/users/${encodeURIComponent(currentEditUser)}`,
                    'PUT',
                    updates
                );

                if (response.ok) {
                    showAlert('User updated successfully', 'success');
                    closeEditModal();
                    loadUsers();
                } else {
                    const error = await response.json();
                    showAlert(error.message || 'Failed to update user', 'error');
                }
            } catch (error) {
                showAlert('Connection error: ' + error.message, 'error');
            }
        }

        // Deactivate user
        async function deactivateUser(uid) {
            if (!confirm(`Are you sure you want to deactivate user: ${uid}?`)) {
                return;
            }

            try {
                const response = await makeAuthenticatedRequest(`/admin/users/${encodeURIComponent(uid)}`, 'DELETE');
                if (response.ok) {
                    showAlert('User deactivated successfully', 'success');
                    loadUsers();
                    loadStats();
                } else {
                    const error = await response.json();
                    showAlert(error.message || 'Failed to deactivate user', 'error');
                }
            } catch (error) {
                showAlert('Connection error: ' + error.message, 'error');
            }
        }

        // Activate user
        async function activateUser(uid) {
            try {
                const response = await makeAuthenticatedRequest(
                    `/admin/users/${encodeURIComponent(uid)}`,
                    'PUT',
                    { active: true }
                );

                if (response.ok) {
                    showAlert('User activated successfully', 'success');
                    loadUsers();
                    loadStats();
                } else {
                    const error = await response.json();
                    showAlert(error.message || 'Failed to activate user', 'error');
                }
            } catch (error) {
                showAlert('Connection error: ' + error.message, 'error');
            }
        }

        // Close edit modal
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            currentEditUser = null;
        }

        // Get CSRF token
        async function getCSRFToken() {
            try {
                const response = await fetch('/csrf-token', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    return data.csrfToken;
                }
            } catch (error) {
                console.warn('Failed to get CSRF token:', error);
            }
            return null;
        }

        // Make authenticated request with JWT tokens
        async function makeAuthenticatedRequest(url, method = 'GET', body = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include' // Include cookies for JWT tokens
            };

            // Add CSRF token for state-changing operations
            if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method.toUpperCase())) {
                const csrfToken = await getCSRFToken();
                if (csrfToken) {
                    options.headers['X-CSRF-Token'] = csrfToken;
                }
            }

            // Add request body if provided
            if (body) {
                options.body = JSON.stringify(body);
            }

            return fetch(url, options);
        }

        // Show alert
        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;

            container.appendChild(alertDiv);

            // Remove alert after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // Handle Enter key in login form and initialize auth
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('adminPassword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    adminLogin();
                }
            });

            // Initialize unified auth client
            setTimeout(() => {
                initUnifiedAuth();
            }, 100);
        });

        // Admin logout
        async function adminLogout() {
            if (!confirm('Are you sure you want to logout?')) {
                return;
            }

            try {
                // Use unified auth client for logout if available
                if (unifiedAuthClient) {
                    await unifiedAuthClient.logout();
                } else {
                    // Fallback to direct API call
                    await fetch('/auth/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include'
                    });
                }

                // Clear admin credentials
                adminCredentials = null;

                // Show auth section and hide main content
                document.getElementById('authSection').style.display = 'block';
                document.getElementById('mainContent').style.display = 'none';

                // Clear form fields
                document.getElementById('adminEmail').value = '';
                document.getElementById('adminPassword').value = '';

                showAlert('Logged out successfully', 'success');
            } catch (error) {
                showAlert('Logout error: ' + error.message, 'error');
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeEditModal();
            }
        }
    </script>
</body>
</html>
