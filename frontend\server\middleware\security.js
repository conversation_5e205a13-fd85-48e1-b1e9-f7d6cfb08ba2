/**
 * Security Middleware
 * Provides additional security features like CSRF protection, security headers, etc.
 */

const crypto = require('crypto');
const rateLimit = require('express-rate-limit');
const auditLogger = require('../utils/audit-logger');

/**
 * CSRF Protection Middleware
 */
class CSRFProtection {
  constructor() {
    this.tokens = new Map(); // In production, use Redis or database
    this.cleanupInterval = setInterval(() => this.cleanupExpiredTokens(), 300000); // 5 minutes
  }

  /**
   * Generate CSRF token
   * @param {string} sessionId - Session ID
   * @returns {string} CSRF token
   */
  generateToken(sessionId) {
    const token = crypto.randomBytes(32).toString('hex');
    const expiry = Date.now() + (3600 * 1000); // 1 hour
    
    this.tokens.set(token, {
      sessionId: sessionId,
      expiry: expiry
    });

    return token;
  }

  /**
   * Validate CSRF token
   * @param {string} token - CSRF token
   * @param {string} sessionId - Session ID
   * @returns {boolean} True if valid
   */
  validateToken(token, sessionId) {
    if (!token || !sessionId) {
      return false;
    }

    const tokenData = this.tokens.get(token);
    if (!tokenData) {
      return false;
    }

    // Check if token has expired
    if (Date.now() > tokenData.expiry) {
      this.tokens.delete(token);
      return false;
    }

    // Check if token belongs to the session
    if (tokenData.sessionId !== sessionId) {
      return false;
    }

    return true;
  }

  /**
   * Cleanup expired tokens
   */
  cleanupExpiredTokens() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [token, data] of this.tokens.entries()) {
      if (now > data.expiry) {
        this.tokens.delete(token);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired CSRF tokens`);
    }
  }

  /**
   * Express middleware for CSRF protection
   * @param {Array} exemptPaths - Paths to exempt from CSRF protection
   * @returns {Function} Express middleware
   */
  middleware(exemptPaths = []) {
    return (req, res, next) => {
      // Skip CSRF for exempt paths
      if (exemptPaths.some(path => req.path.startsWith(path))) {
        return next();
      }

      // Skip CSRF for GET, HEAD, OPTIONS requests
      if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
        return next();
      }

      // Get CSRF token from header or body
      const token = req.headers['x-csrf-token'] || req.body._csrf;
      const sessionId = req.session?.id || req.headers['x-session-id'] || 'anonymous';

      if (!this.validateToken(token, sessionId)) {
        console.log(`❌ CSRF validation failed for ${req.method} ${req.path}`);

        // Log CSRF attack attempt
        auditLogger.logCSRFAttack({
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          url: req.originalUrl,
          method: req.method,
          referer: req.headers.referer,
          uid: req.user ? req.user.uid : 'anonymous'
        });

        return res.status(403).json({
          success: false,
          message: 'CSRF token validation failed',
          code: 'CSRF_INVALID'
        });
      }

      next();
    };
  }

  /**
   * Endpoint to get CSRF token
   * @param {Object} req - Express request
   * @param {Object} res - Express response
   */
  getToken(req, res) {
    const sessionId = req.session?.id || req.headers['x-session-id'] || 'anonymous';
    const token = this.generateToken(sessionId);
    
    res.json({
      success: true,
      csrfToken: token
    });
  }
}

/**
 * Enhanced Security Headers Middleware
 */
class SecurityHeaders {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  /**
   * Get Content Security Policy based on environment
   */
  getCSP() {
    if (this.isDevelopment) {
      // More relaxed CSP for development
      return "default-src 'self'; " +
             "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; " +
             "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
             "font-src 'self' https://fonts.gstatic.com; " +
             "img-src 'self' data: https:; " +
             "connect-src 'self' ws: wss: http://localhost:* https://fonts.googleapis.com https://fonts.gstatic.com; " +
             "frame-ancestors 'none';";
    } else {
      // Strict CSP for production - allow Google Fonts
      return "default-src 'self'; " +
             "script-src 'self' https://cdn.jsdelivr.net; " +
             "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
             "font-src 'self' https://fonts.gstatic.com; " +
             "img-src 'self' data: https:; " +
             "connect-src 'self' wss: https://fonts.googleapis.com https://fonts.gstatic.com; " +
             "frame-ancestors 'none'; " +
             "base-uri 'self'; " +
             "form-action 'self'; " +
             "upgrade-insecure-requests;";
    }
  }

  /**
   * Apply security headers middleware
   */
  middleware() {
    return (req, res, next) => {
      // Content Security Policy
      res.setHeader('Content-Security-Policy', this.getCSP());

      // X-Frame-Options (prevent clickjacking)
      res.setHeader('X-Frame-Options', 'DENY');

      // X-Content-Type-Options (prevent MIME sniffing)
      res.setHeader('X-Content-Type-Options', 'nosniff');

      // X-XSS-Protection (enable XSS filtering)
      res.setHeader('X-XSS-Protection', '1; mode=block');

      // Referrer Policy (control referrer information)
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

      // Permissions Policy (control browser features)
      res.setHeader('Permissions-Policy',
        'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()'
      );

      // Strict Transport Security (HTTPS only in production)
      if (this.isProduction) {
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
      }

      // Cross-Origin Embedder Policy
      res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');

      // Cross-Origin Opener Policy
      res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');

      // Cross-Origin Resource Policy
      res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');

      // X-DNS-Prefetch-Control (control DNS prefetching)
      res.setHeader('X-DNS-Prefetch-Control', 'off');

      // X-Download-Options (prevent file downloads in IE)
      res.setHeader('X-Download-Options', 'noopen');

      // X-Permitted-Cross-Domain-Policies (control Flash/PDF cross-domain access)
      res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');

      // Cache-Control for sensitive pages
      if (req.path.includes('/admin') || req.path.includes('/auth')) {
        res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.setHeader('Surrogate-Control', 'no-store');
      }

      // Remove server information
      res.removeHeader('X-Powered-By');
      res.removeHeader('Server');

      // Add custom security header for SmartTest
      res.setHeader('X-SmartTest-Security', 'enabled');

      next();
    };
  }
}

// Create instance
const securityHeaders = new SecurityHeaders();

// Export the middleware function for backward compatibility
const securityHeadersMiddleware = securityHeaders.middleware();

/**
 * Rate Limiting Configurations
 */
const rateLimiters = {
  // General API rate limiting
  api: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: {
      success: false,
      message: 'Too many requests from this IP, please try again later.',
      code: 'RATE_LIMITED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res, next, options) => {
      // Log rate limit violation
      auditLogger.logRateLimitViolation({
        ip: req.ip || req.connection.remoteAddress,
        endpoint: req.originalUrl,
        attempts: options.max,
        windowMs: options.windowMs,
        userAgent: req.headers['user-agent']
      });

      // Send rate limit response
      res.status(options.statusCode).json({
        success: false,
        message: 'Too many requests from this IP, please try again later.',
        code: 'RATE_LIMITED'
      });
    }
  }),

  // Strict rate limiting for authentication endpoints
  auth: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 login attempts per windowMs
    message: {
      success: false,
      message: 'Too many authentication attempts, please try again later.',
      code: 'AUTH_RATE_LIMITED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res, next, options) => {
      // Log rate limit violation
      auditLogger.logRateLimitViolation({
        ip: req.ip || req.connection.remoteAddress,
        endpoint: req.originalUrl,
        attempts: options.max,
        windowMs: options.windowMs,
        userAgent: req.headers['user-agent']
      });

      // Send auth rate limit response
      res.status(options.statusCode).json({
        success: false,
        message: 'Too many authentication attempts, please try again later.',
        code: 'AUTH_RATE_LIMITED'
      });
    }
  }),

  // Admin endpoints rate limiting
  admin: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 50, // Limit each IP to 50 admin requests per windowMs
    message: {
      success: false,
      message: 'Too many admin requests, please try again later.',
      code: 'ADMIN_RATE_LIMITED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res, next, options) => {
      // Log admin rate limit violation
      auditLogger.logRateLimitViolation({
        ip: req.ip || req.connection.remoteAddress,
        endpoint: req.originalUrl,
        attempts: options.max,
        windowMs: options.windowMs,
        userAgent: req.headers['user-agent']
      });

      // Send admin rate limit response
      res.status(options.statusCode).json({
        success: false,
        message: 'Too many admin requests, please try again later.',
        code: 'ADMIN_RATE_LIMITED'
      });
    }
  })
};

/**
 * Request logging middleware for security monitoring
 */
const securityLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // Log security-relevant information
  const logData = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.headers['user-agent'],
    referer: req.headers.referer,
    sessionId: req.session?.id,
    userId: req.user?.uid
  };

  // Log suspicious patterns
  const suspiciousPatterns = [
    /\.\.\//,  // Directory traversal
    /<script/i, // XSS attempts
    /union.*select/i, // SQL injection
    /javascript:/i, // JavaScript injection
    /eval\(/i, // Code injection
  ];

  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(req.url) || 
    pattern.test(JSON.stringify(req.body)) ||
    pattern.test(JSON.stringify(req.query))
  );

  if (isSuspicious) {
    console.warn('🚨 Suspicious request detected:', logData);
  }

  // Continue with request
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    // Log failed authentication attempts
    if (req.path.includes('auth') && res.statusCode === 401) {
      console.warn('🔐 Failed authentication attempt:', {
        ...logData,
        statusCode: res.statusCode,
        duration: duration
      });
    }
    
    // Log admin access
    if (req.path.includes('admin')) {
      console.log('👑 Admin access:', {
        ...logData,
        statusCode: res.statusCode,
        duration: duration
      });
    }
  });

  next();
};

/**
 * Input sanitization middleware
 */
const sanitizeInput = (req, res, next) => {
  // Sanitize query parameters
  if (req.query) {
    for (const key in req.query) {
      if (typeof req.query[key] === 'string') {
        req.query[key] = req.query[key].trim();
      }
    }
  }

  // Sanitize body parameters
  if (req.body && typeof req.body === 'object') {
    for (const key in req.body) {
      if (typeof req.body[key] === 'string') {
        req.body[key] = req.body[key].trim();
      }
    }
  }

  next();
};

// Create CSRF protection instance
const csrfProtection = new CSRFProtection();

module.exports = {
  CSRFProtection,
  csrfProtection,
  SecurityHeaders,
  securityHeaders: securityHeadersMiddleware,
  rateLimiters,
  securityLogger,
  sanitizeInput
};
