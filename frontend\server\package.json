{"name": "smarttest-api-server", "version": "1.0.0", "description": "API server for SmartTest automation framework", "main": "api.js", "scripts": {"start": "node api.js", "dev": "nodemon api.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:real-db": "jest --testMatch='**/db-real-connection.test.js'", "test:db-connection": "node test-db-connection.js", "test:ssh-connection": "node test-ssh-connection.js", "test:qa01-connection": "node test-qa01-connection.js", "test:qa01-ssh": "node test-qa01-ssh.js", "test:qa01-direct": "node test-qa01-direct.js", "test:db-env": "node test-db-env.js", "test:qa01": "node test-db-env.js qa01", "test:qa02": "node test-db-env.js qa02", "test:qa03": "node test-db-env.js qa03", "test:db-manager": "node test-db-manager.js", "test:db-manager:qa01": "node test-db-manager.js qa01", "test:db-manager:qa01:direct": "node test-db-manager.js qa01 direct", "test:db-manager:qa01:tunnel": "node test-db-manager.js qa01 tunnel", "test:db-manager:qa02": "node test-db-manager.js qa02", "test:db-manager:qa03": "node test-db-manager.js qa03", "test:db-utils": "node test-db-utils.js", "test:db-utils:qa01": "node test-db-utils.js qa01", "test:db-utils:qa02": "node test-db-utils.js qa02", "test:db-utils:qa03": "node test-db-utils.js qa03"}, "dependencies": {"axios": "^1.8.4", "bcrypt": "^6.0.0", "commander": "^14.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.11.2", "fetch-cookie": "^3.1.0", "helmet": "^6.1.5", "jsonwebtoken": "^9.0.2", "mysql2": "^3.2.4", "node-fetch": "^2.7.0", "ssh2": "^1.16.0", "tough-cookie": "^5.1.2", "uuid": "^9.0.0", "validator": "^13.15.15"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["**/*.js", "!node_modules/**", "!coverage/**"], "setupFilesAfterEnv": ["./tests/jest.setup.js"]}}