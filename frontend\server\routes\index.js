/**
 * Routes Index
 * Combines all route modules with JWT-based authentication and role-based access control
 */
const express = require('express');
const router = express.Router();
const { validateCredentials, requirePermission, requireRole } = require('../middleware/auth');
const { validateJWTToken, requirePermissions, requireRoles, trackSessionActivity, autoRefreshToken } = require('../middleware/session-validation');

// Import route modules
const testCasesRoutes = require('./test-cases');
const testSuitesRoutes = require('./test-suites');
const testReportsRoutes = require('./test-reports');
const activeTestsRoutes = require('./active-tests');
const recentRunsRoutes = require('./recent-runs');
const testDetailsRoutes = require('./test-details');
const caseRunnerRoutes = require('./case-runner');
const suiteRunnerRoutes = require('./suite-runner');
const proxyRoutes = require('./proxy-routes');
const testSessionsRoutes = require('./test-sessions');
const inputQueriesRoutes = require('./input-queries');

// Register routes with JWT authentication and role-based access control

// Apply session activity tracking and auto-refresh to all routes
router.use(trackSessionActivity);
router.use(autoRefreshToken);

// Local routes - require JWT authentication and read permission
router.use('/local', validateJWTToken, requirePermissions('read'), testCasesRoutes);
router.use('/local', validateJWTToken, requirePermissions('read'), activeTestsRoutes);
router.use('/local', validateJWTToken, requirePermissions('read'), recentRunsRoutes);
router.use('/local', validateJWTToken, requirePermissions('read'), testDetailsRoutes);

// API routes - require JWT authentication and appropriate permissions
router.use('/api', validateJWTToken, requirePermissions('read'), testSuitesRoutes);
router.use('/api', validateJWTToken, requirePermissions('read'), testReportsRoutes);
router.use('/api', validateJWTToken, requirePermissions('write'), caseRunnerRoutes);
router.use('/api', validateJWTToken, requirePermissions('write'), suiteRunnerRoutes);

// External API routes - no JWT authentication required for login endpoint
router.use('/api', proxyRoutes);

// AutoRun routes - require JWT authentication and write permission for test execution
router.use('/AutoRun', validateJWTToken, requirePermissions('write'), testSessionsRoutes);
router.use('/AutoRun', validateJWTToken, requirePermissions('write'), inputQueriesRoutes);

// Note: Backward compatibility routes have been removed as part of the server refactoring
// All frontend code now uses the correct endpoints directly

module.exports = router;
