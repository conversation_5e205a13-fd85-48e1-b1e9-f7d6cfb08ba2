/* Test Runner Styles - Microsoft Teams UI Compatible */

:root {
    /* Teams color palette */
    --teams-base: #f3f2f1;
    --teams-primary: #6264a7;
    --teams-primary-hover: #7174b4;
    --teams-success: #92c353;
    --teams-danger: #d13438;
    --teams-warning: #ffaa44;
    --teams-info: #2d8cff;
    --teams-dark: #252423;
    --teams-light: #ffffff;
    --teams-border: #e1dfdd;
    --teams-text: #252423;
    --teams-text-light: #f3f2f1;
}

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
    background-color: var(--teams-base);
    color: var(--teams-text);
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow-x: hidden;
}

/* Header */
.ms-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--teams-primary);
    color: var(--teams-light);
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.ms-header-brand {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--teams-light);
    text-decoration: none;
}

.ms-header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ms-environment-display {
    font-size: 0.875rem;
    color: var(--teams-light);
}

.ms-user-info {
    font-size: 0.875rem;
    color: var(--teams-light);
    opacity: 0.9;
}

/* Layout */
.ms-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px);
}

.ms-layout {
    display: flex;
    flex: 1;
}

/* Navigation */
.ms-nav {
    width: 240px;
    background-color: var(--teams-light);
    border-right: 1px solid var(--teams-border);
    overflow-y: auto;
}

.ms-nav-content {
    padding: 1rem 0;
}

.ms-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ms-nav-item {
    margin: 0;
}

.ms-nav-link {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--teams-text);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 400;
    transition: background-color 0.2s;
}

.ms-nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.ms-nav-link-active {
    background-color: rgba(98, 100, 167, 0.1);
    border-left: 3px solid var(--teams-primary);
    font-weight: 600;
    color: var(--teams-primary);
}

/* Content */
.ms-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.ms-content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-section-title {
    margin: 1.5rem 0 1rem;
    font-weight: 600;
}

/* Grid */
.ms-grid {
    display: flex;
    flex-direction: column;
    margin: 0 -0.75rem;
    width: 100%;
}

.ms-grid-row {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 1.5rem;
    align-items: stretch;
    justify-content: space-between;
}

.ms-grid-col {
    padding: 0 0.75rem;
    margin-bottom: 1.5rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.ms-sm3 {
    width: 25%;
    flex: 0 0 calc(25% - 1rem);
    max-width: calc(25% - 1rem);
}

.ms-sm4 {
    width: 33.333%;
    flex: 0 0 calc(33.333% - 1rem);
    max-width: calc(33.333% - 1rem);
}

.ms-sm6 {
    width: 50%;
    flex: 0 0 calc(50% - 1rem);
    max-width: calc(50% - 1rem);
}

.ms-sm8 {
    width: 66.666%;
    flex: 0 0 calc(66.666% - 1rem);
    max-width: calc(66.666% - 1rem);
}

/* Cards */
.ms-card {
    background-color: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    margin-bottom: 1.5rem;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.ms-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ms-card.highlighted {
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 2px rgba(98, 100, 167, 0.3), 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.ms-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--teams-border);
}

.ms-card-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--teams-text);
    flex: 1;
    padding-right: 0.5rem;
    line-height: 1.3;
}

.ms-card-activity {
    font-size: 0.875rem;
    color: var(--teams-text);
    opacity: 0.7;
}

.ms-card-count {
    font-weight: 600;
    color: var(--teams-primary);
}

.ms-card-body {
    margin-bottom: 0.75rem;
}

/* Forms */
.ms-form-group {
    margin-bottom: 1rem;
}

.ms-Label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--teams-text);
    margin-bottom: 0.25rem;
}

.ms-TextField-field {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--teams-border);
    border-radius: 2px;
    font-size: 0.875rem;
    font-family: inherit;
    background-color: var(--teams-light);
    color: var(--teams-text);
    transition: border-color 0.2s;
}

.ms-TextField-field:focus {
    outline: none;
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 1px var(--teams-primary);
}

.ms-TextField-description {
    font-size: 0.75rem;
    color: var(--teams-text);
    opacity: 0.7;
    margin-top: 0.25rem;
}

.ms-Dropdown {
    position: relative;
}

.ms-Dropdown-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--teams-border);
    border-radius: 2px;
    font-size: 0.875rem;
    font-family: inherit;
    background-color: var(--teams-light);
    color: var(--teams-text);
    cursor: pointer;
}

.ms-Dropdown-select:focus {
    outline: none;
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 1px var(--teams-primary);
}

/* Buttons */
.ms-Button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-radius: 2px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    min-height: 32px;
    box-sizing: border-box;
}

.ms-Button--primary {
    background-color: var(--teams-primary);
    color: var(--teams-light);
    border-color: var(--teams-primary);
}

.ms-Button--primary:hover {
    background-color: var(--teams-primary-hover);
    border-color: var(--teams-primary-hover);
}

.ms-Button--default {
    background-color: var(--teams-light);
    color: var(--teams-text);
    border-color: var(--teams-border);
}

.ms-Button--default:hover {
    background-color: var(--teams-base);
    border-color: var(--teams-primary);
}

.ms-Button-label {
    font-weight: inherit;
}

/* Tables */
.ms-Table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.ms-Table th,
.ms-Table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid var(--teams-border);
}

.ms-Table th {
    font-weight: 600;
    background-color: var(--teams-base);
    color: var(--teams-text);
}

/* Recent Runs Table with Sticky Header */
#recent-runs-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    table-layout: fixed;
}

#recent-runs-table thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--teams-base);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#recent-runs-table thead tr {
    width: 100%;
}

#recent-runs-table thead th {
    padding: 0.75rem 0.5rem;
    font-weight: 600;
    background-color: var(--teams-base);
    color: var(--teams-text);
    border-bottom: 2px solid var(--teams-border);
    text-align: left;
    white-space: nowrap;
}

#recent-runs-table th:first-child {
    width: 10%; /* Test ID */
}

#recent-runs-table th:nth-child(2) {
    width: 12%; /* TSN ID */
}

#recent-runs-table th:nth-child(3) {
    width: 10%; /* Status */
}

#recent-runs-table th:nth-child(4) {
    width: 33%; /* Start time */
}

#recent-runs-table th:last-child {
    width: 35%; /* Actions */
}

#recent-runs-table tbody {
    width: 100%;
}

#recent-runs-table tbody tr {
    width: 100%;
    border-bottom: 1px solid var(--teams-border);
}

#recent-runs-table tbody tr:hover {
    background-color: rgba(98, 100, 167, 0.05);
}

#recent-runs-table tbody td {
    padding: 0.75rem 0.5rem;
    text-align: left;
    border-bottom: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#recent-runs-table tbody td:first-child {
    font-weight: 600;
    color: var(--teams-primary);
}

#recent-runs-table tbody td:nth-child(2) {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

#recent-runs-table tbody td:nth-child(4) {
    font-size: 0.8rem;
    color: var(--teams-text);
    opacity: 0.8;
}

#recent-runs-table tbody td:last-child {
    text-align: center;
}

/* Scrollbar styling for the table */
#recent-runs-table tbody::-webkit-scrollbar {
    width: 6px;
}

#recent-runs-table tbody::-webkit-scrollbar-track {
    background: var(--teams-base);
    border-radius: 3px;
}

#recent-runs-table tbody::-webkit-scrollbar-thumb {
    background: var(--teams-border);
    border-radius: 3px;
}

#recent-runs-table tbody::-webkit-scrollbar-thumb:hover {
    background: var(--teams-primary);
}

/* Test Statistics */
.test-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.test-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--teams-border);
}

.test-stat:last-child {
    border-bottom: none;
}

.test-stat-label {
    font-size: 0.875rem;
    color: var(--teams-text);
}

.test-stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--teams-text);
}

.test-passed {
    color: var(--teams-success);
}

.test-failed {
    color: var(--teams-danger);
}

/* Modal */
.ms-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.ms-modal.active {
    display: flex;
}

.ms-modal-content {
    background-color: var(--teams-light);
    border-radius: 4px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.ms-modal-content h2 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: var(--teams-text);
}

.ms-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

/* Empty State */
.ms-empty-message {
    text-align: center;
    color: var(--teams-text);
    opacity: 0.7;
    padding: 2rem;
    font-style: italic;
}

/* Panel Content */
.ms-panel-content {
    padding: 1rem;
}

/* Search Results */
.search-results {
    max-height: 400px;
    overflow-y: auto;
}

/* Active Tests */
.active-tests {
    max-height: 400px;
    overflow-y: auto;
}

/* Card Footer Optimization */
.enhanced-active-test-card .ms-card-footer {
    padding-top: 8px;
    display: flex;
    justify-content: flex-end; /* Align button to the right */
}

.enhanced-active-test-card .ms-card-footer .ms-Button {
    font-size: 11px; /* Smaller button text */
    padding: 4px 12px; /* Smaller button padding */
    min-height: 24px; /* Smaller button height */
}

/* Enhanced Active Test Cards */
.enhanced-active-test-card {
    margin-bottom: 12px;
    border-left: 4px solid var(--teams-primary);
    transition: all 0.3s ease;
    max-width: 400px; /* Limit maximum width */
    width: fit-content; /* Use only needed width */
    min-width: 320px; /* Ensure minimum readable width */
}

.enhanced-active-test-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.enhanced-active-test-card .ms-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-bottom: 8px;
    gap: 12px; /* Add gap between sections */
}

.test-info-section {
    flex: 1;
    min-width: 0; /* Allow text truncation */
}

.test-title {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    gap: 6px; /* Reduce gap between icon and text */
}

.status-icon {
    font-size: 14px; /* Slightly smaller icon */
    flex-shrink: 0; /* Prevent icon from shrinking */
}

.test-name {
    font-weight: 600;
    color: var(--teams-text);
    font-size: 13px; /* Slightly smaller text */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.test-details {
    display: flex;
    gap: 8px; /* Reduce gap between details */
    font-size: 10px; /* Smaller details text */
    color: var(--teams-text);
    opacity: 0.7;
    flex-wrap: wrap; /* Allow wrapping on small screens */
}

.test-status-section {
    text-align: right;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 3px; /* Reduce gap */
    flex-shrink: 0; /* Prevent shrinking */
}



.test-status-badge {
    padding: 1px 6px; /* Smaller padding */
    border-radius: 10px; /* Smaller border radius */
    font-size: 9px; /* Smaller badge text */
    font-weight: 600;
    text-transform: uppercase;
    white-space: nowrap; /* Prevent badge from wrapping */
}

.test-status-badge.ms-status-running {
    background-color: #e1f5fe;
    color: #0277bd;
}

.test-status-badge.ms-status-passed {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.test-status-badge.ms-status-failed {
    background-color: #ffebee;
    color: #c62828;
}

.test-status-badge.ms-status-error {
    background-color: #fff3e0;
    color: #ef6c00;
}



/* Test Notifications */
.test-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 4px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    animation: slideInRight 0.3s ease;
}

.test-notification.test-started {
    border-left: 4px solid #4caf50;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-icon {
    font-size: 16px;
}

.notification-text {
    font-size: 14px;
    color: var(--teams-text);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
    .enhanced-active-test-card {
        min-width: 280px; /* Smaller minimum width on mobile */
        max-width: 100%; /* Allow full width on small screens */
    }

    .test-details {
        flex-direction: column; /* Stack details vertically on small screens */
        gap: 2px;
    }

    .test-name {
        font-size: 12px; /* Even smaller text on mobile */
    }
}

/* Compact layout for Active Tests panel */
.active-tests .ms-panel-content {
    display: flex;
    flex-direction: column;
    gap: 8px; /* Consistent spacing between cards */
}




.status-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-height: auto;
}

/* Floating Action Button */
.floating-test-runner {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.fab-main {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--teams-primary);
    color: var(--teams-light);
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.fab-main:hover {
    background-color: var(--teams-primary-hover);
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.fab-icon {
    font-size: 1.25rem;
}

.quick-test-panel {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 320px;
    background-color: var(--teams-light);
    border: 1px solid var(--teams-border);
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(10px);
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.quick-test-panel:not(.hidden) {
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.quick-test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--teams-border);
}

.quick-test-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--teams-text);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--teams-text);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background-color: var(--teams-base);
    border-radius: 2px;
}

.quick-test-body {
    padding: 1rem;
}

.quick-input-group {
    margin-bottom: 1rem;
}

.quick-input-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--teams-text);
    margin-bottom: 0.25rem;
}

.quick-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--teams-border);
    border-radius: 2px;
    font-size: 0.875rem;
    font-family: inherit;
    background-color: var(--teams-light);
    color: var(--teams-text);
    box-sizing: border-box;
}

.quick-input:focus {
    outline: none;
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 1px var(--teams-primary);
}

.quick-settings-info {
    background-color: var(--teams-base);
    border-radius: 4px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.settings-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.settings-label {
    font-weight: 600;
    color: var(--teams-text);
}

.settings-note {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--teams-text);
    opacity: 0.7;
}

.quick-actions {
    display: flex;
    justify-content: flex-end;
}

.quick-run-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--teams-primary);
    color: var(--teams-light);
    border: none;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.quick-run-btn:hover {
    background-color: var(--teams-primary-hover);
}

.btn-icon {
    font-size: 1rem;
}

/* Status Text Styling for Test Results */
.status-passed {
    color: #28a745;
    font-weight: 600;
}

.status-failed {
    color: #dc3545;
    font-weight: 600;
}

.status-running {
    color: #007bff;
    font-weight: 600;
}

.status-pending {
    color: #ffc107;
    font-weight: 600;
}

.status-cancelled {
    color: #6c757d;
    font-weight: 600;
}

.status-timeout {
    color: #dc3545;
    font-weight: 600;
}

.status-error {
    color: #dc3545;
    font-weight: 600;
}

/* Table Container Fix - Prevent Double Scrollbars */
.table-container {
    overflow-x: hidden !important;
    overflow-y: auto;
    max-height: 625px;
    position: relative;
}

.table-container .ms-Table {
    width: 100% !important;
    table-layout: fixed;
    border-collapse: collapse;
    margin: 0;
}

.table-container .ms-Table th,
.table-container .ms-Table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0.5rem;
    vertical-align: middle;
}

/* Ensure table body doesn't create its own scrollbar */
.table-container .ms-Table tbody {
    overflow: visible;
}

/* Fix for Recent Runs Table specifically */
#recent-runs-table {
    min-width: 0 !important;
    width: 100% !important;
}

#recent-runs-table th,
#recent-runs-table td {
    min-width: 0;
    max-width: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ms-nav {
        width: 200px;
    }

    .ms-content {
        padding: 1rem;
    }

    .ms-grid-row {
        flex-direction: column;
    }

    .ms-sm3,
    .ms-sm4,
    .ms-sm6,
    .ms-sm8 {
        width: 100%;
        flex: none;
        max-width: none;
    }

    .floating-test-runner {
        bottom: 1rem;
        right: 1rem;
    }

    .quick-test-panel {
        width: 280px;
    }
}
