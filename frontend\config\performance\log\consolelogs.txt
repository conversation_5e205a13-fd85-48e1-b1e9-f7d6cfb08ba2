Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
unified-api-service.js:297 Fetch failed loading: GET "http://localhost:3000/local/recent-runs?limit=100".
getRequest @ unified-api-service.js:297
getRecentRuns @ unified-api-service.js:761
(anonymous) @ simple-optimizations.js:211
executeNewRequest @ simple-optimizations.js:45
executeRequest @ simple-optimizations.js:37
window.apiService.getRecentRuns @ simple-optimizations.js:209
(anonymous) @ simple-optimizations.js:267
poll @ simple-optimizations.js:145
(anonymous) @ simple-optimizations.js:136
setInterval
startPolling @ simple-optimizations.js:135
setupConfigPolling @ simple-optimizations.js:262
setTimeout
(anonymous) @ simple-optimizations.js:306
(anonymous) @ simple-optimizations.js:308
unified-auth-client.js:501 ✅ All stored authentication data cleared
unified-auth-client.js:179 ✅ Logout completed
unified-auth-client.js:472 ✅ JWT authentication state stored for: <EMAIL>
unified-auth-client.js:436 ⏰ Automatic refresh scheduled in 780 seconds
unified-auth-client.js:359 ⏰ Periodic session validation started (every 300 seconds)
unified-auth-client.js:113 ✅ Login successful for: <EMAIL>
config-auth.js:143 Login successful for: <EMAIL>
unified-api-service.js:183 API credentials set for user: <EMAIL>, Stored in sessionStorage: true
unified-api-service.js:278 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:283 Making GET request to: http://localhost:3000/local/recent-runs
unified-api-service.js:278 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:283 Making GET request to: http://localhost:3000/local/recent-runs
3simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
config.js:386 Received 50 recent runs update
config.js:492 processRecentRunsData called with 50 runs
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:775 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:776 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:794 Rendering 50 recent runs
config.js:386 Received 50 recent runs update
config.js:492 processRecentRunsData called with 50 runs
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:775 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:776 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:794 Rendering 50 recent runs
config.js:861 viewTestDetails called for TSN: 17653
test-details-modal.js:62 Using JWT credentials from unified auth client: <EMAIL>
test-details-modal.js:206 Attempting to load test details from external API first...
reports.details-external.js:3 Loading test details from external API for 17653...
reports.details-external.js:7 🔧 External API Service found: true
reports.details-external.js:8 🔧 Service type: externalApiService
external-api-service.js:61 [ExternalApiService] 🔍 Session validity check: true (expires in 1474s)
reports.details-external.js:16 🔐 Session valid: true
reports.details-external.js:48 📡 Fetching report summary...
reports.details-external.js:63 🔑 External API session available: Explicit ID
reports.details-external.js:68 📡 Making request with credentials: include to ensure cookies are sent
config.js:970 View Details clicked for TSN ID: 17653
test-details-modal.js:62 Using JWT credentials from unified auth client: <EMAIL>
test-details-modal.js:206 Attempting to load test details from external API first...
reports.details-external.js:3 Loading test details from external API for 17653...
reports.details-external.js:7 🔧 External API Service found: true
reports.details-external.js:8 🔧 Service type: externalApiService
external-api-service.js:61 [ExternalApiService] 🔍 Session validity check: true (expires in 1474s)
reports.details-external.js:16 🔐 Session valid: true
reports.details-external.js:48 📡 Fetching report summary...
reports.details-external.js:63 🔑 External API session available: Explicit ID
reports.details-external.js:68 📡 Making request with credentials: include to ensure cookies are sent
reports.details-external.js:71 📡 Summary response status: 200
reports.details-external.js:72 📡 Summary response headers: {access-control-allow-origin: '*', connection: 'keep-alive', content-length: '731', content-security-policy: "default-src 'self'; script-src 'self' https://cdn.…'; form-action 'self'; upgrade-insecure-requests;", content-type: 'text/html; charset=utf-8', …}
reports.details-external.js:142 📄 Summary HTML length: 731
reports.details-external.js:146 🔄 External API returned login page, session may have expired. Attempting re-authentication...
loadTestDetailsFromExternalApi @ reports.details-external.js:146
await in loadTestDetailsFromExternalApi
show @ test-details-modal.js:209
viewTestDetails @ config.js:877
(anonymous) @ config.js:837Understand this warning
external-api-service.js:75 [ExternalApiService] 🔑 Attempting login to external <NAME_EMAIL>...
external-api-service.js:81 [ExternalApiService] 🔗 Using external login endpoint: /api/external/login
external-api-service.js:83 [ExternalApiService] 📡 Making login request to /api/external/login
reports.details-external.js:71 📡 Summary response status: 200
reports.details-external.js:72 📡 Summary response headers: {access-control-allow-origin: '*', connection: 'keep-alive', content-length: '731', content-security-policy: "default-src 'self'; script-src 'self' https://cdn.…'; form-action 'self'; upgrade-insecure-requests;", content-type: 'text/html; charset=utf-8', …}
reports.details-external.js:142 📄 Summary HTML length: 731
reports.details-external.js:146 🔄 External API returned login page, session may have expired. Attempting re-authentication...
loadTestDetailsFromExternalApi @ reports.details-external.js:146
await in loadTestDetailsFromExternalApi
show @ test-details-modal.js:209
handleViewDetailsClick @ config.js:979Understand this warning
external-api-service.js:75 [ExternalApiService] 🔑 Attempting login to external <NAME_EMAIL>...
external-api-service.js:81 [ExternalApiService] 🔗 Using external login endpoint: /api/external/login
external-api-service.js:83 [ExternalApiService] 📡 Making login request to /api/external/login
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
external-api-service.js:92 [ExternalApiService] 📡 Login response status: 200
external-api-service.js:93 [ExternalApiService] 📡 Login response headers: {access-control-allow-origin: '*', connection: 'keep-alive', content-length: '704', content-security-policy: "default-src 'self'; script-src 'self' https://cdn.…'; form-action 'self'; upgrade-insecure-requests;", content-type: 'text/html; charset=utf-8', …}
external-api-service.js:100 [ExternalApiService] 🍪 Checking for JSESSIONID cookie...
external-api-service.js:104 [ExternalApiService] 🍪 Document cookies: AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; sessionId=6398e74234bf17f917165819525f0ff6d781afe0bb4cb321975bb5a190e72baa; JSESSIONID=2A606B519D4E93345491799B0DD8CFF0
external-api-service.js:109 [ExternalApiService] ✅ Found JSESSIONID in document.cookie: 2A606B519D4E93345491799B0DD8CFF0
external-api-service.js:132 [ExternalApiService] 📄 Response text length: 704
external-api-service.js:149 [ExternalApiService] 🔍 Login success indicators: {hasValidJsessionId: true, jsessionId: '2A606B51...', includesUid: false, includesWelcome: false, includesLogout: false, …}
external-api-service.js:162 [ExternalApiService] ✅ Login successful
reports.details-external.js:155 🔄 Retrying summary request after re-authentication...
external-api-service.js:92 [ExternalApiService] 📡 Login response status: 200
external-api-service.js:93 [ExternalApiService] 📡 Login response headers: {access-control-allow-origin: '*', connection: 'keep-alive', content-length: '704', content-security-policy: "default-src 'self'; script-src 'self' https://cdn.…'; form-action 'self'; upgrade-insecure-requests;", content-type: 'text/html; charset=utf-8', …}
external-api-service.js:100 [ExternalApiService] 🍪 Checking for JSESSIONID cookie...
external-api-service.js:104 [ExternalApiService] 🍪 Document cookies: AMP_985eaa9c45=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4Y2U3NTJhYS0yNTMwLTQzZWQtYTAyNS0zNWI4ZGZhYTQ5ZWIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzUzMTkxODgxMzM4JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJwYWdlQ291bnRlciUyMiUzQTAlN0Q=; sessionId=6398e74234bf17f917165819525f0ff6d781afe0bb4cb321975bb5a190e72baa; JSESSIONID=DC75E18CC93309E3EF1C4230D2AE5B85
external-api-service.js:109 [ExternalApiService] ✅ Found JSESSIONID in document.cookie: DC75E18CC93309E3EF1C4230D2AE5B85
external-api-service.js:132 [ExternalApiService] 📄 Response text length: 704
external-api-service.js:149 [ExternalApiService] 🔍 Login success indicators: {hasValidJsessionId: true, jsessionId: 'DC75E18C...', includesUid: false, includesWelcome: false, includesLogout: false, …}
external-api-service.js:162 [ExternalApiService] ✅ Login successful
reports.details-external.js:155 🔄 Retrying summary request after re-authentication...
simple-optimizations.js:47 Request completed: recentRuns_{"limit":100}
6simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
reports.details-external.js:163 📄 Retry Summary HTML length: 731
reports.details-external.js:306 ❌ EXTERNAL API ONLY MODE - Error in loadTestDetailsFromExternalApi for 17653: Still receiving login page after re-authentication Error: Still receiving login page after re-authentication
    at loadTestDetailsFromExternalApi (reports.details-external.js:167:23)
    at async TestDetailsModal.show (test-details-modal.js:209:35)
loadTestDetailsFromExternalApi @ reports.details-external.js:306
await in loadTestDetailsFromExternalApi
show @ test-details-modal.js:209
viewTestDetails @ config.js:877
(anonymous) @ config.js:837Understand this error
reports.details-external.js:307 🚫 NO DATABASE FALLBACK - External API must be fixed to load test details
loadTestDetailsFromExternalApi @ reports.details-external.js:307
await in loadTestDetailsFromExternalApi
show @ test-details-modal.js:209
viewTestDetails @ config.js:877
(anonymous) @ config.js:837Understand this error
test-details-modal.js:213 External API failed, trying other APIs: EXTERNAL API ONLY: Failed to load details for 17653 from external API: Still receiving login page after re-authentication. Database fallback is disabled.
test-details-modal.js:238 ConfigApiService not available, trying database API...
reports.details-db.js:3 Loading test details from database API for test 17653...
reports.details-db.js:9 Using ApiService to load test details
unified-api-service.js:783 Getting test details for 17653 using endpoint /local/test-details
unified-api-service.js:278 Constructing URL: Base=http://localhost:3000, Endpoint=/local/test-details/17653, Full URL=http://localhost:3000/local/test-details/17653
unified-api-service.js:283 Making GET request to: http://localhost:3000/local/test-details/17653
reports.details-external.js:163 📄 Retry Summary HTML length: 731
reports.details-external.js:306 ❌ EXTERNAL API ONLY MODE - Error in loadTestDetailsFromExternalApi for 17653: Still receiving login page after re-authentication Error: Still receiving login page after re-authentication
    at loadTestDetailsFromExternalApi (reports.details-external.js:167:23)
    at async TestDetailsModal.show (test-details-modal.js:209:35)
loadTestDetailsFromExternalApi @ reports.details-external.js:306
await in loadTestDetailsFromExternalApi
show @ test-details-modal.js:209
handleViewDetailsClick @ config.js:979Understand this error
reports.details-external.js:307 🚫 NO DATABASE FALLBACK - External API must be fixed to load test details
loadTestDetailsFromExternalApi @ reports.details-external.js:307
await in loadTestDetailsFromExternalApi
show @ test-details-modal.js:209
handleViewDetailsClick @ config.js:979Understand this error
test-details-modal.js:213 External API failed, trying other APIs: EXTERNAL API ONLY: Failed to load details for 17653 from external API: Still receiving login page after re-authentication. Database fallback is disabled.
test-details-modal.js:238 ConfigApiService not available, trying database API...
reports.details-db.js:3 Loading test details from database API for test 17653...
reports.details-db.js:9 Using ApiService to load test details
simple-optimizations.js:32 Request deduplication: testDetails_17653
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100}
simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
unified-api-service.js:278 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:283 Making GET request to: http://localhost:3000/local/recent-runs
unified-api-service.js:278 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:283 Making GET request to: http://localhost:3000/local/recent-runs
4simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
unified-api-service.js:786 [DEBUG] getTestDetails: Raw API response: {success: true, test: {…}, data: {…}, message: 'Test details retrieved successfully'}
unified-api-service.js:791 [DEBUG] getTestDetails: Found test in response.test
unified-api-service.js:806 [DEBUG] getTestDetails: Test cases in result: 3
unified-api-service.js:808 [DEBUG] getTestDetails: First test case: {tc_id: '3180', test_case_name: 'Check pending feeds', description: 'check NO pending feeds\r\\n\r\\nbk copy:  3228', outcome: 'P', status: 'Passed', …}
simple-optimizations.js:47 Request completed: testDetails_17653
reports.details-db.js:26 currentState not available, skipping state storage
loadTestDetailsFromDatabaseApi @ reports.details-db.js:26
await in loadTestDetailsFromDatabaseApi
show @ test-details-modal.js:241
await in show
viewTestDetails @ config.js:877
(anonymous) @ config.js:837Understand this warning
reports.details-db.js:29 Test details loaded from database API via ApiService: {tsn_id: '17653', test_id: '3180', test_name: 'Check pending feeds', type: 'Test Case', envir: 'qa02', …}
reports.details-db.js:26 currentState not available, skipping state storage
loadTestDetailsFromDatabaseApi @ reports.details-db.js:26
await in loadTestDetailsFromDatabaseApi
show @ test-details-modal.js:241
await in show
handleViewDetailsClick @ config.js:979Understand this warning
reports.details-db.js:29 Test details loaded from database API via ApiService: {tsn_id: '17653', test_id: '3180', test_name: 'Check pending feeds', type: 'Test Case', envir: 'qa02', …}
test-details-modal.js:242 Successfully loaded from database API: {tsn_id: '17653', test_id: '3180', test_name: 'Check pending feeds', type: 'Test Case', envir: 'qa02', …}
unified-api-service.js:297 Fetch finished loading: GET "http://localhost:3000/local/test-details/17653?".
getRequest @ unified-api-service.js:297
getTestDetails @ unified-api-service.js:785
(anonymous) @ simple-optimizations.js:231
executeNewRequest @ simple-optimizations.js:45
executeRequest @ simple-optimizations.js:37
window.apiService.getTestDetails @ simple-optimizations.js:229
loadTestDetailsFromDatabaseApi @ reports.details-db.js:17
show @ test-details-modal.js:241
await in show
viewTestDetails @ config.js:877
(anonymous) @ config.js:837
test-details-modal.js:242 Successfully loaded from database API: {tsn_id: '17653', test_id: '3180', test_name: 'Check pending feeds', type: 'Test Case', envir: 'qa02', …}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
config.js:386 Received 50 recent runs update
config.js:492 processRecentRunsData called with 50 runs
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:775 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:776 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:794 Rendering 50 recent runs
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
config.js:386 Received 50 recent runs update
config.js:492 processRecentRunsData called with 50 runs
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:775 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:776 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:794 Rendering 50 recent runs
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":100}
6simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100}
simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:394 Received 0 active tests update
config.js:445 Updating active tests display with 0 tests
config.js:590 🔄 renderActiveTests called, active tests count: 0
unified-api-service.js:278 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:283 Making GET request to: http://localhost:3000/local/recent-runs
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
unified-api-service.js:278 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:283 Making GET request to: http://localhost:3000/local/recent-runs
4simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}