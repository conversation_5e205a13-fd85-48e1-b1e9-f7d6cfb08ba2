# SessionStorage Key Usage Audit

## Current SessionStorage Key Patterns

### Standardized Keys (Consistent Usage)
These keys are used consistently across multiple modules:

#### `smarttest_uid` - User Identifier
- **Usage**: Primary user identifier for authentication
- **Files**:
  - `frontend/shared/services/unified-api-service.js` (lines 164, 196)
  - `frontend/shared/auth/unified-auth-client.js` (lines 34, 466, 485)
  - `frontend/shared/services/base-api-service.js` (lines 27, 43)
  - `frontend/config/js/test-details-modal.js` (line 86)
  - `frontend/config/config-auth.js` (line 249)
  - `frontend/reports/reports.init.js` (line 255)
  - `frontend/config/api-explorer.js` (lines 40, 60)

#### `smarttest_pwd` - User Password
- **Usage**: Encrypted/hashed password storage
- **Files**:
  - `frontend/shared/services/unified-api-service.js` (lines 165, 197)
  - `frontend/shared/auth/unified-auth-client.js` (lines 486)
  - `frontend/shared/services/base-api-service.js` (line 28)
  - `frontend/reports/reports.init.js` (line 256)
  - `frontend/config/api-explorer.js` (line 61)

#### `smarttest_session_id` - JWT Session ID
- **Usage**: JWT session identifier
- **Files**:
  - `frontend/shared/auth/unified-auth-client.js` (lines 35, 467, 487)

#### `smarttest_session_expiry` - Session Expiration
- **Usage**: JWT session expiration timestamp
- **Files**:
  - `frontend/shared/auth/unified-auth-client.js` (lines 36, 468, 488)

#### `smarttest_user` - User Object
- **Usage**: Complete user object storage
- **Files**:
  - `frontend/shared/auth/unified-auth-client.js` (lines 37, 469, 489)

#### `smarttest_permissions` - User Permissions
- **Usage**: User permission array
- **Files**:
  - `frontend/shared/auth/unified-auth-client.js` (lines 38, 470, 490)

### Inconsistent Keys (Need Standardization)

#### `currentUser` - Inconsistent User Identifier
- **Usage**: Alternative user identifier (INCONSISTENT)
- **Files**:
  - `frontend/dashboard/api-integration.js` (line 2167)
- **Issue**: Uses different key than standard `smarttest_uid`

### Legacy Keys (LocalStorage)

#### `userCredentials` - Legacy Credential Storage
- **Usage**: Legacy credential storage in localStorage
- **Files**:
  - `frontend/config/js/test-details-modal.js` (line 95)
  - `frontend/shared/auth/unified-auth-client.js` (line 493)
- **Status**: Being phased out in favor of sessionStorage

#### `smarttest_auth` - Legacy Auth Storage
- **Usage**: Legacy authentication storage
- **Files**:
  - `frontend/shared/auth/unified-auth-client.js` (line 494)
- **Status**: Being phased out

## Standardization Issues Identified

### Issue 1: Mixed Key Usage for User ID
- **Standard**: `smarttest_uid` (used in 7+ files)
- **Inconsistent**: `currentUser` (used in 1 file)
- **Impact**: Authentication lookup failures when modules use different keys

### Issue 2: Fallback Chain Complexity
Multiple files implement complex fallback chains:
1. Try sessionStorage with standard keys
2. Try sessionStorage with legacy keys  
3. Try localStorage with legacy keys
4. Try window.apiService credentials

This creates maintenance overhead and potential inconsistencies.

## Recommended Standardization

### Phase 1: Eliminate `currentUser` Key
**Target File**: `frontend/dashboard/api-integration.js`
**Change**: Replace `sessionStorage.getItem('currentUser')` with `sessionStorage.getItem('smarttest_uid')`

### Phase 2: Simplify Fallback Chains
**Target Files**: All files with complex fallback logic
**Change**: Standardize on sessionStorage first, then window.apiService fallback only

### Phase 3: Remove Legacy Keys
**Target**: All localStorage usage
**Change**: Migrate remaining localStorage usage to sessionStorage with standard keys

## Implementation Priority

1. **High Priority**: Fix `currentUser` → `smarttest_uid` inconsistency
2. **Medium Priority**: Simplify fallback chains
3. **Low Priority**: Remove legacy localStorage keys (backward compatibility)

## Risk Assessment

- **Risk Level**: Low
- **Backward Compatibility**: Maintained through fallback mechanisms
- **Testing Required**: Verify authentication works across all modules
- **Rollback Plan**: Simple key name reversion
