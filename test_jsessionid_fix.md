# JSESSIONID Fix Testing Guide

## Test Scenarios

### Test 1: Case Sensitivity
**Objective**: Verify that user IDs with different cases work correctly

**Steps**:
1. Login with uppercase: `TESTUSER`
2. Make API call (should use lowercase: `testuser`)
3. Verify JSESSIONID is found in cache

**Expected Result**: API call succeeds with cached JSESSIONID

### Test 2: Whitespace Handling
**Objective**: Verify that user IDs with whitespace are handled correctly

**Steps**:
1. Login with whitespace: ` testuser `
2. Make API call (should use trimmed: `testuser`)
3. Verify JSESSIONID is found in cache

**Expected Result**: API call succeeds with cached JSESSIONID

### Test 3: Mixed Case and Whitespace
**Objective**: Verify complex normalization scenarios

**Steps**:
1. Login with: ` TestUser `
2. Make API call (should use: `testuser`)
3. Verify JSESSIONID is found in cache

**Expected Result**: API call succeeds with cached JSESSIONID

## Verification Points

### Login Flow Logs
Look for: `[External Login] 🔧 Updating cookie-auth cache for user: [original] (normalized: [normalized])`

### API Flow Logs
Look for: `[External ReportSummary] 🔍 Checking cached session for user: [original] (normalized: [normalized])`

### Success Indicators
- `✅ Using cached JSESSIONID: [session]... (SOURCE: CACHE)`
- No fallback to browser cookies when cache should have the session
- Consistent session propagation across all API calls

## Manual Testing Commands

```bash
# Test the server endpoints directly
curl -X POST http://localhost:3000/api/external/login \
  -H "Content-Type: application/json" \
  -d '{"uid": " TestUser ", "password": "test"}'

# Then test API call with JWT token
curl -X GET http://localhost:3000/api/external/report-summary/12345 \
  -H "Authorization: Bearer [jwt-token]"
```

## Automated Testing

The fix can be verified by checking the server logs during normal application usage:
1. Start the server with logging enabled
2. Login through the web interface with various user ID formats
3. Navigate to reports to trigger API calls
4. Check logs for consistent normalization messages

## Rollback Plan

If issues are detected:
1. Revert the 3 changes in `frontend/server/routes/proxy-routes.js`
2. Remove normalization logic
3. Return to original user ID handling

The changes are isolated and can be easily reverted without affecting other functionality.
